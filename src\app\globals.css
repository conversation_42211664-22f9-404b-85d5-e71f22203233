@import "tailwindcss";

@theme {
  /* Your custom colors - simplified */
  --color-primary: #0b0505;    /* Your dark background color */
  --color-secondary: #ffead6;  /* Your light text color */
  --color-accent: #f26430;     /* Your accent color */

  /* Your custom fonts */
  --font-sans: var(--font-open-sans);
  --font-heading: var(--font-poppins);
}

html {
  scroll-behavior: smooth;
}

html, body {
  height: 100%;
  height: 100dvh;
}

:root {
  /* CSS variables for compatibility */
  --color-primary-rgb: 11, 5, 5;
  --color-background: #0b0505;    /* Your dark background */
  --color-foreground: #ffead6;    /* Your light text */
  --color-accent: #f26430;        /* Your accent color */
  --font-sans: var(--font-open-sans);
  --font-heading: var(--font-poppins);
}

body {
  background: var(--color-background);
  color: var(--color-foreground);
  font-family: var(--font-sans);
}

.scroll-down-arrow {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Safari mobile viewport fix */
.h-screen {
  height: 100vh;
  height: 100dvh;
}

.min-h-screen {
  min-height: 100vh;
  min-height: 100dvh;
}

/* Navbar hover effects */
.navbar-item {
  position: relative;
  transition: color 0.3s ease;
}

.navbar-item::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: var(--color-accent);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.navbar-item:hover::after {
  width: 100%;
}

.request-project-btn {
  position: relative;
  overflow: hidden;
  transition: color 0.3s ease;
}

.request-project-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: var(--color-accent);
  border-radius: 50%;
  transition: all 0.4s ease;
  transform: translate(-50%, -50%);
  z-index: -1;
}

.request-project-btn:hover::before {
  width: 200%;
  height: 200%;
}

.request-project-btn:hover {
  color: var(--color-secondary);
}
